/**
 * 极简认证中间件 - 统一处理 Google One Tap 和 Neon Auth
 */

import { redirect } from "@remix-run/cloudflare";
import { type AuthUser, extractJwtFromCookies, verifyJwt } from "./jwt.server";

// 认证结果接口
export interface AuthResult {
  success: boolean;
  user?: AuthUser & { jwt: string };
  error?: string;
}

/**
 * 核心认证函数 - requireUser
 * 这是唯一需要的认证函数，所有 Loader 都调用它
 */
export async function requireUser(request: Request): Promise<AuthUser & { jwt: string }> {
  // 从 Cookie 中提取 JWT
  const jwt = extractJwtFromCookies(request);

  if (!jwt) {
    throw redirect("/auth/login");
  }

  // 验证 JWT
  const result = await verifyJwt(jwt);

  if (!result.success || !result.user) {
    throw redirect("/auth/login");
  }

  // 返回用户信息和 JWT（用于数据库查询）
  return { ...result.user, jwt };
}

/**
 * 可选的非强制认证检查
 * 返回用户信息或 null，不会重定向
 */
export async function getUser(request: Request): Promise<AuthResult> {
  try {
    const jwt = extractJwtFromCookies(request);

    if (!jwt) {
      return { success: false, error: "No JWT found" };
    }

    const result = await verifyJwt(jwt);

    if (!result.success || !result.user) {
      return { success: false, error: result.error || "Invalid JWT" };
    }

    return {
      success: true,
      user: { ...result.user, jwt },
    };
  } catch (error) {
    console.error("Auth check error:", error);
    return { success: false, error: "Authentication failed" };
  }
}

/**
 * API专用认证函数 - 返回JSON错误而不是重定向
 * 用于API路由，不会重定向到登录页面
 */
export async function requireUserForAPI(request: Request): Promise<AuthUser & { jwt: string }> {
  // 从 Cookie 中提取 JWT
  const jwt = extractJwtFromCookies(request);

  if (!jwt) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: "Authentication required",
        message: "Please log in to access this resource",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  // 验证 JWT
  const result = await verifyJwt(jwt);

  if (!result.success || !result.user) {
    throw new Response(
      JSON.stringify({
        success: false,
        error: "Invalid authentication",
        message: "Your session has expired. Please log in again",
        code: 401,
      }),
      {
        status: 401,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }

  // 返回用户信息和 JWT（用于数据库查询）
  return { ...result.user, jwt };
}
